// Advanced Game Configuration & Constants
const GAME_CONFIG = Object.freeze({
    GRID_SIZE: 20,
    CANVAS_SIZE: 600,
    INITIAL_SNAKE_LENGTH: 3,
    PARTICLE_SYSTEMS: {
        FOOD_CONSUMPTION: { count: 25, velocity: 4, life: 2.0 },
        MOVEMENT_TRAIL: { count: 3, velocity: 1, life: 0.8 },
        COLLISION_EXPLOSION: { count: 50, velocity: 6, life: 3.0 }
    },
    PHYSICS: {
        FRICTION: 0.98,
        GRAVITY: 0.1,
        BOUNCE_DAMPING: 0.7
    },
    RENDERING: {
        SHADOW_BLUR: 15,
        GLOW_INTENSITY: 0.8,
        ANIMATION_EASING: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    }
});

// Advanced Game State Management System
class GameStateManager {
    constructor() {
        this.states = new Map();
        this.currentState = null;
        this.previousState = null;
        this.stateTransitions = new Map();
    }

    registerState(name, stateObject) {
        this.states.set(name, stateObject);
    }

    transition(newState, data = {}) {
        if (this.currentState) {
            this.previousState = this.currentState;
            this.states.get(this.currentState)?.onExit?.(data);
        }
        this.currentState = newState;
        this.states.get(newState)?.onEnter?.(data);
    }

    update(deltaTime) {
        this.states.get(this.currentState)?.update?.(deltaTime);
    }

    render(ctx) {
        this.states.get(this.currentState)?.render?.(ctx);
    }
}

// Sophisticated Particle System with Physics
class AdvancedParticleSystem {
    constructor() {
        this.particles = [];
        this.emitters = new Map();
        this.forces = [];
    }

    createEmitter(id, config) {
        this.emitters.set(id, {
            position: config.position || { x: 0, y: 0 },
            rate: config.rate || 10,
            lifetime: config.lifetime || 1000,
            particleConfig: config.particleConfig || {},
            lastEmit: 0
        });
    }

    emit(emitterId, count = 1) {
        const emitter = this.emitters.get(emitterId);
        if (!emitter) return;

        for (let i = 0; i < count; i++) {
            this.particles.push(new AdvancedParticle({
                x: emitter.position.x + (Math.random() - 0.5) * 20,
                y: emitter.position.y + (Math.random() - 0.5) * 20,
                vx: (Math.random() - 0.5) * emitter.particleConfig.velocity || 2,
                vy: (Math.random() - 0.5) * emitter.particleConfig.velocity || 2,
                life: emitter.particleConfig.life || 1.0,
                color: emitter.particleConfig.color || '#ffffff',
                size: emitter.particleConfig.size || Math.random() * 3 + 1,
                type: emitter.particleConfig.type || 'default'
            }));
        }
    }

    update(deltaTime) {
        this.particles = this.particles.filter(particle => {
            particle.update(deltaTime, this.forces);
            return particle.life > 0;
        });
    }

    render(ctx) {
        this.particles.forEach(particle => particle.render(ctx));
    }
}

class AdvancedParticle {
    constructor(config) {
        Object.assign(this, config);
        this.initialLife = this.life;
        this.age = 0;
        this.rotation = Math.random() * Math.PI * 2;
        this.rotationSpeed = (Math.random() - 0.5) * 0.1;
    }

    update(deltaTime, forces = []) {
        this.age += deltaTime;
        this.life -= deltaTime * 0.001;

        forces.forEach(force => {
            this.vx += force.x * deltaTime;
            this.vy += force.y * deltaTime;
        });

        this.vx *= GAME_CONFIG.PHYSICS.FRICTION;
        this.vy *= GAME_CONFIG.PHYSICS.FRICTION;
        this.vy += GAME_CONFIG.PHYSICS.GRAVITY * deltaTime * 0.001;

        this.x += this.vx * deltaTime * 0.1;
        this.y += this.vy * deltaTime * 0.1;
        this.rotation += this.rotationSpeed * deltaTime * 0.01;

        const lifeRatio = this.life / this.initialLife;
        this.currentSize = this.size * Math.sin(lifeRatio * Math.PI);
    }

    render(ctx) {
        const alpha = Math.max(0, this.life / this.initialLife);
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);

        ctx.shadowBlur = 10;
        ctx.shadowColor = this.color;
        ctx.fillStyle = this.color;

        if (this.type === 'spark') {
            ctx.beginPath();
            ctx.moveTo(-this.currentSize, 0);
            ctx.lineTo(this.currentSize, 0);
            ctx.lineWidth = 2;
            ctx.strokeStyle = this.color;
            ctx.stroke();
        } else {
            ctx.beginPath();
            ctx.arc(0, 0, this.currentSize, 0, Math.PI * 2);
            ctx.fill();
        }

        ctx.restore();
    }
}

// Legacy compatibility layer
let canvas, ctx;
let snake = [];
let food = {};
let direction = { x: 1, y: 0 };
let nextDirection = { x: 1, y: 0 };
let gameRunning = false;
let score = 0;
let highScore = 0;
let gameSpeed = 150;
let gameLoop;
let particles = [];
let foodGlow = 0;
let snakeTrail = [];
let moveProgress = 0;
let lastMoveTime = 0;

// Advanced Input Management System
class InputManager {
    constructor() {
        this.keys = new Map();
        this.keyBindings = new Map();
        this.inputBuffer = [];
        this.bufferSize = 10;
        this.lastInputTime = 0;
    }

    registerKeyBinding(key, action) {
        this.keyBindings.set(key.toLowerCase(), action);
    }

    handleKeyDown(event) {
        const key = event.key.toLowerCase();
        this.keys.set(key, true);

        const currentTime = performance.now();
        this.inputBuffer.push({ key, time: currentTime, type: 'down' });

        if (this.inputBuffer.length > this.bufferSize) {
            this.inputBuffer.shift();
        }

        const action = this.keyBindings.get(key);
        if (action) {
            action(event);
            event.preventDefault();
        }
    }

    handleKeyUp(event) {
        const key = event.key.toLowerCase();
        this.keys.delete(key);
    }

    isKeyPressed(key) {
        return this.keys.has(key.toLowerCase());
    }

    getInputHistory() {
        return [...this.inputBuffer];
    }
}

// Advanced Audio Management System
class AudioManager {
    constructor() {
        this.audioContext = null;
        this.sounds = new Map();
        this.masterVolume = 0.5;
        this.initialized = false;
    }

    async initialize() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.initialized = true;
            this.createSynthesizedSounds();
        } catch (error) {
            console.warn('Audio initialization failed:', error);
        }
    }

    createSynthesizedSounds() {
        this.sounds.set('eat', this.createTone(440, 0.1, 'sine'));
        this.sounds.set('move', this.createTone(220, 0.05, 'square'));
        this.sounds.set('gameOver', this.createTone(110, 0.5, 'sawtooth'));
    }

    createTone(frequency, duration, type = 'sine') {
        return () => {
            if (!this.audioContext) return;

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(this.masterVolume * 0.1, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        };
    }

    play(soundName) {
        const sound = this.sounds.get(soundName);
        if (sound && this.initialized) {
            sound();
        }
    }
}

// Performance Monitoring System
class PerformanceMonitor {
    constructor() {
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
        this.frameTime = 0;
        this.memoryUsage = 0;
        this.updateInterval = 60;
    }

    update() {
        this.frameCount++;
        const currentTime = performance.now();
        this.frameTime = currentTime - this.lastTime;

        if (this.frameCount % this.updateInterval === 0) {
            this.fps = Math.round(1000 / (this.frameTime / this.updateInterval));

            if (performance.memory) {
                this.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1048576);
            }
        }

        this.lastTime = currentTime;
    }

    getMetrics() {
        return {
            fps: this.fps,
            frameTime: this.frameTime.toFixed(2),
            memoryUsage: this.memoryUsage
        };
    }
}

// Advanced Game Initialization
function initGame() {
    canvas = document.getElementById('gameCanvas');
    ctx = canvas.getContext('2d');

    // Enable advanced canvas features
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    highScore = localStorage.getItem('snakeHighScore') || 0;
    document.getElementById('highScore').textContent = highScore;

    // Initialize advanced input system
    const inputManager = new InputManager();
    inputManager.registerKeyBinding('w', () => handleDirectionChange('up'));
    inputManager.registerKeyBinding('arrowup', () => handleDirectionChange('up'));
    inputManager.registerKeyBinding('s', () => handleDirectionChange('down'));
    inputManager.registerKeyBinding('arrowdown', () => handleDirectionChange('down'));
    inputManager.registerKeyBinding('a', () => handleDirectionChange('left'));
    inputManager.registerKeyBinding('arrowleft', () => handleDirectionChange('left'));
    inputManager.registerKeyBinding('d', () => handleDirectionChange('right'));
    inputManager.registerKeyBinding('arrowright', () => handleDirectionChange('right'));

    document.addEventListener('keydown', (e) => inputManager.handleKeyDown(e));
    document.addEventListener('keyup', (e) => inputManager.handleKeyUp(e));
    document.getElementById('speed').addEventListener('change', changeSpeed);

    // Initialize particle system
    particles = new AdvancedParticleSystem();

    resetGame();
    startGame();
}

function handleDirectionChange(dir) {
    if (!gameRunning) return;

    const directionMap = {
        'up': { x: 0, y: -1, opposite: 'down' },
        'down': { x: 0, y: 1, opposite: 'up' },
        'left': { x: -1, y: 0, opposite: 'right' },
        'right': { x: 1, y: 0, opposite: 'left' }
    };

    const newDir = directionMap[dir];
    const currentDir = Object.keys(directionMap).find(key =>
        directionMap[key].x === direction.x && directionMap[key].y === direction.y
    );

    if (newDir && newDir.opposite !== currentDir) {
        nextDirection = { x: newDir.x, y: newDir.y };
    }
}

// Advanced Snake Entity System
class SnakeEntity {
    constructor(x, y) {
        this.position = { x, y };
        this.velocity = { x: 0, y: 0 };
        this.acceleration = { x: 0, y: 0 };
        this.segments = [];
        this.health = 100;
        this.energy = 100;
        this.powerUps = new Set();
        this.lastUpdateTime = performance.now();
    }

    addSegment(x, y) {
        this.segments.push({
            position: { x, y },
            age: 0,
            size: GAME_CONFIG.GRID_SIZE * 0.4,
            color: this.getSegmentColor(this.segments.length)
        });
    }

    getSegmentColor(index) {
        const hue = (120 + index * 5) % 360;
        return `hsl(${hue}, 70%, 50%)`;
    }

    update(deltaTime) {
        this.segments.forEach((segment, index) => {
            segment.age += deltaTime;
            const ageRatio = Math.min(segment.age / 1000, 1);
            segment.size = GAME_CONFIG.GRID_SIZE * 0.4 * (0.8 + 0.2 * Math.sin(ageRatio * Math.PI));
        });
    }
}

// Advanced Food System with Multiple Types
class FoodSystem {
    constructor() {
        this.foods = [];
        this.foodTypes = {
            normal: { points: 10, color: '#ff6b6b', size: 1.0, rarity: 0.7 },
            golden: { points: 50, color: '#ffd93d', size: 1.2, rarity: 0.2 },
            power: { points: 25, color: '#6bcf7f', size: 1.1, rarity: 0.08 },
            toxic: { points: -20, color: '#8b5cf6', size: 0.9, rarity: 0.02 }
        };
    }

    generateFood() {
        const rand = Math.random();
        let cumulativeRarity = 0;
        let selectedType = 'normal';

        for (const [type, config] of Object.entries(this.foodTypes)) {
            cumulativeRarity += config.rarity;
            if (rand <= cumulativeRarity) {
                selectedType = type;
                break;
            }
        }

        return {
            x: Math.floor(Math.random() * (GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE)),
            y: Math.floor(Math.random() * (GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE)),
            type: selectedType,
            config: this.foodTypes[selectedType],
            spawnTime: performance.now(),
            pulsePhase: Math.random() * Math.PI * 2
        };
    }

    updateFood(food, deltaTime) {
        food.pulsePhase += deltaTime * 0.005;
        const age = performance.now() - food.spawnTime;

        if (food.type === 'toxic' && age > 10000) {
            return false;
        }

        return true;
    }
}

// Advanced Game Reset with Entity Management
function resetGame() {
    snake = [];
    const centerX = Math.floor(GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE / 2);
    const centerY = Math.floor(GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE / 2);

    // Initialize snake with advanced entity system
    const snakeEntity = new SnakeEntity(centerX, centerY);
    for (let i = 0; i < GAME_CONFIG.INITIAL_SNAKE_LENGTH; i++) {
        snake.push({
            x: centerX - i,
            y: centerY,
            entity: snakeEntity,
            segmentIndex: i,
            renderData: {
                scale: 1.0,
                rotation: 0,
                opacity: 1.0,
                lastPosition: { x: centerX - i, y: centerY }
            }
        });
        snakeEntity.addSegment(centerX - i, centerY);
    }

    direction = { x: 1, y: 0 };
    nextDirection = { x: 1, y: 0 };
    score = 0;
    moveProgress = 0;
    lastMoveTime = 0;

    // Initialize advanced food system
    const foodSystem = new FoodSystem();
    food = foodSystem.generateFood();

    // Reset particle systems
    if (particles && particles.particles) {
        particles.particles.length = 0;
    }

    updateScore();
    gameRunning = true;

    document.getElementById('gameOver').style.display = 'none';
}

function generateFood() {
    do {
        food = {
            x: Math.floor(Math.random() * (GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE)),
            y: Math.floor(Math.random() * (GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE)),
            type: 'normal',
            config: { points: 10, color: '#ff6b6b', size: 1.0 },
            spawnTime: performance.now(),
            pulsePhase: Math.random() * Math.PI * 2
        };
    } while (isSnakePosition(food.x, food.y));
}

function isSnakePosition(x, y) {
    return snake.some(segment => segment.x === x && segment.y === y);
}

function update(currentTime) {
    if (!gameRunning) return;

    if (lastMoveTime === 0) lastMoveTime = currentTime;

    const timeSinceLastMove = currentTime - lastMoveTime;
    moveProgress = Math.min(timeSinceLastMove / gameSpeed, 1);

    if (moveProgress >= 1) {
        direction = { ...nextDirection };

        const head = { ...snake[0] };
        head.x += direction.x;
        head.y += direction.y;

        if (head.x < 0 || head.x >= GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE ||
            head.y < 0 || head.y >= GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE) {
            endGame();
            return;
        }

        if (isSnakePosition(head.x, head.y)) {
            endGame();
            return;
        }

        snake.unshift(head);

        if (head.x === food.x && head.y === food.y) {
            score += food.config.points;
            updateScore();

            // Advanced particle emission
            if (particles && particles.emit) {
                particles.emit('foodConsumption', GAME_CONFIG.PARTICLE_SYSTEMS.FOOD_CONSUMPTION.count);
            } else {
                // Fallback particle creation
                for (let i = 0; i < 15; i++) {
                    createParticle(
                        food.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE/2,
                        food.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE/2,
                        food.config.color
                    );
                }
            }

            generateFood();

            if (score % 50 === 0 && gameSpeed > 50) {
                gameSpeed = Math.max(50, gameSpeed - 5);
            }
        } else {
            const tail = snake.pop();
            createParticle(
                tail.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE/2,
                tail.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE/2,
                'rgba(0, 255, 100, 0.5)'
            );
        }

        moveProgress = 0;
        lastMoveTime = currentTime;
    }

    // Update advanced particle system
    if (particles && particles.update) {
        particles.update(currentTime - lastMoveTime);
    }
}

// Advanced particle creation with physics
function createParticle(x, y, color) {
    if (Array.isArray(particles)) {
        particles.push({
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * 4,
            vy: (Math.random() - 0.5) * 4,
            life: 1.0,
            initialLife: 1.0,
            color: color,
            size: Math.random() * 3 + 1,
            rotation: Math.random() * Math.PI * 2,
            rotationSpeed: (Math.random() - 0.5) * 0.1,
            type: 'default'
        });
    }
}

// Advanced particle physics update
function updateParticles() {
    if (Array.isArray(particles)) {
        particles = particles.filter(particle => {
            // Advanced physics simulation
            particle.vx *= GAME_CONFIG.PHYSICS.FRICTION;
            particle.vy *= GAME_CONFIG.PHYSICS.FRICTION;
            particle.vy += GAME_CONFIG.PHYSICS.GRAVITY * 0.01;

            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= 0.02;
            particle.rotation += particle.rotationSpeed;

            // Advanced size animation
            const lifeRatio = particle.life / particle.initialLife;
            particle.currentSize = particle.size * Math.sin(lifeRatio * Math.PI);

            return particle.life > 0;
        });
    }
}

// Advanced particle rendering with effects
function drawParticles() {
    if (Array.isArray(particles)) {
        particles.forEach(particle => {
            ctx.save();
            ctx.globalAlpha = particle.life;
            ctx.translate(particle.x, particle.y);
            ctx.rotate(particle.rotation);

            // Advanced particle glow effect
            ctx.shadowBlur = 5;
            ctx.shadowColor = particle.color;
            ctx.fillStyle = particle.color;

            if (particle.type === 'spark') {
                // Advanced spark rendering
                ctx.beginPath();
                ctx.moveTo(-particle.currentSize, 0);
                ctx.lineTo(particle.currentSize, 0);
                ctx.lineWidth = 2;
                ctx.strokeStyle = particle.color;
                ctx.stroke();
            } else {
                // Advanced circular particle
                ctx.beginPath();
                ctx.arc(0, 0, particle.currentSize, 0, Math.PI * 2);
                ctx.fill();
            }

            ctx.restore();
        });
    }
}

function draw() {
    // Clear canvas with advanced background
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, GAME_CONFIG.CANVAS_SIZE, GAME_CONFIG.CANVAS_SIZE);

    // Draw advanced grid
    ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
    ctx.lineWidth = 0.5;
    for (let i = 0; i <= GAME_CONFIG.CANVAS_SIZE; i += GAME_CONFIG.GRID_SIZE) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, GAME_CONFIG.CANVAS_SIZE);
        ctx.moveTo(0, i);
        ctx.lineTo(GAME_CONFIG.CANVAS_SIZE, i);
        ctx.stroke();
    }

    // Update and render advanced particle systems
    if (particles && particles.update && particles.render) {
        particles.update(16.67); // 60 FPS delta time
        particles.render(ctx);
    } else {
        drawParticles();
    }

    // Advanced snake rendering system
    if (snake.length > 0) {
        const radius = GAME_CONFIG.GRID_SIZE * 0.4;

        // Draw snake body with advanced rendering
        for (let i = 0; i < snake.length; i++) {
            let renderX = snake[i].x;
            let renderY = snake[i].y;

            if (i === 0) {
                renderX = snake[i].x - direction.x * (1 - moveProgress);
                renderY = snake[i].y - direction.y * (1 - moveProgress);
            }

            const x = renderX * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE/2;
            const y = renderY * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE/2;

            if (i === 0) {
                // Advanced head rendering
                const headGradient = ctx.createRadialGradient(x, y - radius/3, 0, x, y, radius);
                headGradient.addColorStop(0, '#9ACD32');
                headGradient.addColorStop(0.5, '#6B8E23');
                headGradient.addColorStop(1, '#2F4F2F');
                ctx.fillStyle = headGradient;

                ctx.beginPath();
                ctx.arc(x, y, radius + 2, 0, Math.PI * 2);
                ctx.fill();

                // Advanced eye rendering
                ctx.fillStyle = '#000000';
                const eyeOffset = radius * 0.4;
                ctx.beginPath();
                ctx.arc(x - eyeOffset, y - radius * 0.3, 3, 0, Math.PI * 2);
                ctx.arc(x + eyeOffset, y - radius * 0.3, 3, 0, Math.PI * 2);
                ctx.fill();

                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.arc(x - eyeOffset + 1, y - radius * 0.3 - 1, 1, 0, Math.PI * 2);
                ctx.arc(x + eyeOffset + 1, y - radius * 0.3 - 1, 1, 0, Math.PI * 2);
                ctx.fill();
            } else {
                // Advanced body segment rendering
                const alpha = Math.max(0.6, 1 - (i * 0.02));
                ctx.save();
                ctx.globalAlpha = alpha;

                const bodyGradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
                bodyGradient.addColorStop(0, '#8FBC8F');
                bodyGradient.addColorStop(0.5, '#6B8E23');
                bodyGradient.addColorStop(1, '#2F4F2F');
                ctx.fillStyle = bodyGradient;

                ctx.beginPath();
                ctx.arc(x, y, radius, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }
    }

    // Advanced food rendering system
    if (food) {
        const foodX = food.x * GAME_CONFIG.GRID_SIZE;
        const foodY = food.y * GAME_CONFIG.GRID_SIZE;
        const pulseIntensity = Math.sin(food.pulsePhase || 0) * 0.2 + 1;
        const foodRadius = (GAME_CONFIG.GRID_SIZE/2 - 2) * pulseIntensity;

        // Advanced food gradient based on type
        const foodColor = food.config ? food.config.color : '#ff6b6b';
        const foodGradient = ctx.createRadialGradient(
            foodX + GAME_CONFIG.GRID_SIZE/2 - 2,
            foodY + GAME_CONFIG.GRID_SIZE/2 - 2,
            0,
            foodX + GAME_CONFIG.GRID_SIZE/2,
            foodY + GAME_CONFIG.GRID_SIZE/2,
            foodRadius
        );
        foodGradient.addColorStop(0, foodColor);
        foodGradient.addColorStop(0.7, foodColor + '88');
        foodGradient.addColorStop(1, foodColor + '44');
        ctx.fillStyle = foodGradient;

        ctx.beginPath();
        ctx.arc(
            foodX + GAME_CONFIG.GRID_SIZE/2,
            foodY + GAME_CONFIG.GRID_SIZE/2,
            foodRadius,
            0,
            Math.PI * 2
        );
        ctx.fill();

        // Advanced food highlight
        ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
        ctx.beginPath();
        ctx.arc(
            foodX + GAME_CONFIG.GRID_SIZE/2 - 3,
            foodY + GAME_CONFIG.GRID_SIZE/2 - 3,
            2,
            0,
            Math.PI * 2
        );
        ctx.fill();

        // Update food animation
        if (food.pulsePhase !== undefined) {
            food.pulsePhase += 0.1;
        }
    }
}

function handleKeyPress(event) {
    if (!gameRunning) return;

    const key = event.key.toLowerCase();

    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        event.preventDefault();
    }

    switch (key) {
        case 'arrowup':
        case 'w':
            if (direction.y !== 1) nextDirection = { x: 0, y: -1 };
            break;
        case 'arrowdown':
        case 's':
            if (direction.y !== -1) nextDirection = { x: 0, y: 1 };
            break;
        case 'arrowleft':
        case 'a':
            if (direction.x !== 1) nextDirection = { x: -1, y: 0 };
            break;
        case 'arrowright':
        case 'd':
            if (direction.x !== -1) nextDirection = { x: 1, y: 0 };
            break;
    }
}

function changeSpeed() {
    gameSpeed = parseInt(document.getElementById('speed').value);
}

function updateScore() {
    document.getElementById('score').textContent = score;
}

function endGame() {
    gameRunning = false;

    for (let i = 0; i < 30; i++) {
        createParticle(
            snake[0].x * GRID_SIZE + GRID_SIZE/2,
            snake[0].y * GRID_SIZE + GRID_SIZE/2,
            `hsl(${Math.random() * 60}, 100%, 50%)`
        );
    }

    let isNewHighScore = false;
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('snakeHighScore', highScore);
        document.getElementById('highScore').textContent = highScore;
        isNewHighScore = true;
    }

    document.getElementById('finalScore').textContent = score;
    document.getElementById('newHighScore').style.display = isNewHighScore ? 'block' : 'none';
    document.getElementById('gameOver').style.display = 'block';
}

function restartGame() {
    gameRunning = false;
    resetGame();
    startGameLoop();
}

function startGameLoop() {
    function gameLoopFrame(currentTime) {
        if (gameRunning) {
            update(currentTime);
            draw();
            requestAnimationFrame(gameLoopFrame);
        }
    }
    requestAnimationFrame(gameLoopFrame);
}

function startGame() {
    draw();
    startGameLoop();
}

window.addEventListener('load', initGame);